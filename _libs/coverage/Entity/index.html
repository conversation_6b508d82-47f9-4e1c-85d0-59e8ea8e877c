<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Entity</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">Entity</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="success">Total</td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="81.32" aria-valuemin="0" aria-valuemax="100" style="width: 81.32%">
           <span class="sr-only">81.32% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">81.32%</div></td>
       <td class="success small"><div align="right">344&nbsp;/&nbsp;423</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="78.87" aria-valuemin="0" aria-valuemax="100" style="width: 78.87%">
           <span class="sr-only">78.87% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">78.87%</div></td>
       <td class="success small"><div align="right">112&nbsp;/&nbsp;142</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="14.29" aria-valuemin="0" aria-valuemax="100" style="width: 14.29%">
           <span class="sr-only">14.29% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">14.29%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;7</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportColumn.php.html">ExportColumn.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">30&nbsp;/&nbsp;30</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">16&nbsp;/&nbsp;16</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="sr-only">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportData.php.html">ExportData.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="91.92" aria-valuemin="0" aria-valuemax="100" style="width: 91.92%">
           <span class="sr-only">91.92% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">91.92%</div></td>
       <td class="success small"><div align="right">91&nbsp;/&nbsp;99</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="sr-only">83.33% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.33%</div></td>
       <td class="success small"><div align="right">20&nbsp;/&nbsp;24</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportHeader.php.html">ExportHeader.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="98.48" aria-valuemin="0" aria-valuemax="100" style="width: 98.48%">
           <span class="sr-only">98.48% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">98.48%</div></td>
       <td class="success small"><div align="right">65&nbsp;/&nbsp;66</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="95.45" aria-valuemin="0" aria-valuemax="100" style="width: 95.45%">
           <span class="sr-only">95.45% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">95.45%</div></td>
       <td class="success small"><div align="right">21&nbsp;/&nbsp;22</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportRecord.php.html">ExportRecord.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.59" aria-valuemin="0" aria-valuemax="100" style="width: 92.59%">
           <span class="sr-only">92.59% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.59%</div></td>
       <td class="success small"><div align="right">50&nbsp;/&nbsp;54</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="92.31" aria-valuemin="0" aria-valuemax="100" style="width: 92.31%">
           <span class="sr-only">92.31% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">92.31%</div></td>
       <td class="success small"><div align="right">24&nbsp;/&nbsp;26</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportTable.php.html">ExportTable.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="86.84" aria-valuemin="0" aria-valuemax="100" style="width: 86.84%">
           <span class="sr-only">86.84% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">86.84%</div></td>
       <td class="success small"><div align="right">33&nbsp;/&nbsp;38</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="76.19" aria-valuemin="0" aria-valuemax="100" style="width: 76.19%">
           <span class="sr-only">76.19% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">76.19%</div></td>
       <td class="success small"><div align="right">16&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportTableCollection.php.html">ExportTableCollection.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="12.12" aria-valuemin="0" aria-valuemax="100" style="width: 12.12%">
           <span class="sr-only">12.12% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">12.12%</div></td>
       <td class="danger small"><div align="right">8&nbsp;/&nbsp;66</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="23.81" aria-valuemin="0" aria-valuemax="100" style="width: 23.81%">
           <span class="sr-only">23.81% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">23.81%</div></td>
       <td class="danger small"><div align="right">5&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="ExportValue.php.html">ExportValue.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="95.71" aria-valuemin="0" aria-valuemax="100" style="width: 95.71%">
           <span class="sr-only">95.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">95.71%</div></td>
       <td class="success small"><div align="right">67&nbsp;/&nbsp;70</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="sr-only">83.33% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">83.33%</div></td>
       <td class="success small"><div align="right">10&nbsp;/&nbsp;12</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Wed Jun 25 7:10:56 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
